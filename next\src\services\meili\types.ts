// eslint-disable-next-line import/no-relative-packages
import { config } from '../../../../strapi/config/plugins.meilisearch.config'
import {
  ArticleEntityFragment,
  DocumentEntityFragment,
  InbaArticleEntityFragment,
  InbaReleaseEntityFragment,
  Page,
  PageEntityFragment,
  RegulationEntityFragment,
} from '../graphql'

/**
 * A type that describes an entity wrapped in shared search index.
 * E.g.:
 * {
 *     type: "article",
 *     article: {...}
 * }
 */
export type SearchIndexWrapped<T extends string, K extends object> = {
  [key in T]: K
} & {
  type: T
}

export type MixedResults =
  | SearchIndexWrapped<'page', PageMeili>
  | SearchIndexWrapped<'article', ArticleMeili>
  | SearchIndexWrapped<'regulation', RegulationMeili>

export type PageMeili = PageEntityFragment & Page['publishedAt']

type ArticlesPopulatedField = typeof config.article.entriesQuery.populate

// Utility type for making specified fields partial
type MakeFieldsPartial<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// Define relational fields that are NOT populated in Meilisearch
type UnpopulatedRelationalFields = 'files' | 'gallery' | 'inbaRelease'

export type ArticleMeili = MakeFieldsPartial<ArticleEntityFragment, UnpopulatedRelationalFields>

export type DocumentMeili = DocumentEntityFragment

export type InbaArticleMeili = InbaArticleEntityFragment

export type InbaReleaseMeili = InbaReleaseEntityFragment

export type RegulationMeili = RegulationEntityFragment
